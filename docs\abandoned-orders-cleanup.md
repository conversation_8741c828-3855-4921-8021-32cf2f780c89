# Sistema di Gestione Ordini Abbandonati

## Panoramica

Il sistema di gestione degli ordini abbandonati include due componenti principali:
1. **Cart Recovery System**: Invia email di recupero carrello dopo 2 ore per incoraggiare il completamento dell'ordine
2. **Cleanup System**: Pulisce automaticamente gli ordini abbandonati dopo 24 ore

Questo sistema risolve il problema degli ordini creati durante il checkout ma mai completati, offrendo una seconda possibilità ai clienti prima della cancellazione definitiva.

## Comportamento Attuale

### Quando viene creato un ordine
1. L'utente compila il form di checkout
2. Viene creato un Payment Intent su Stripe
3. **L'ordine viene salvato nel database con status `pending`**
4. Se l'utente abbandona il checkout, l'ordine rimane pending

### Problemi risolti
- **Database pollution**: Accumulo di ordini non completati
- **Analytics distortion**: Ordini pending che distorcono le statistiche
- **Inventory management**: Chiarezza sullo stato degli ordini
- **Performance**: Riduzione di record non necessari

## Componenti del Sistema

### 1. Cart Recovery System

#### API di Order Management Unificata (Cron)
**Endpoint**: `/api/cron/order-management`

- Eseguito automaticamente ogni giorno alle 02:00 (limitazioni piano gratuito Vercel)
- **Cart Recovery**: Invia email di recupero per ordini pending da 2+ ore
- **Cleanup**: Cancella ordini pending da 24+ ore
- Utilizza il sistema di rilevamento lingua dell'utente
- Marca gli ordini come "recovery email sent" per evitare duplicati

#### Template Email Multilingua
- **Italiano**: Email incoraggiante con spiegazione gamification
- **Francese**: Versione francese con stesso contenuto
- **Tedesco**: Versione tedesca (default)
- Include link diretto al checkout con parametro recovery
- Spiega i vantaggi del sistema fedeltà (punti, regali, sconti)

### 2. Cleanup System

#### API di Cleanup Manuale
**Endpoint**: `/api/admin/cleanup/abandoned-orders`

- **GET**: Controlla ordini abbandonati senza cancellarli
- **POST**: Cancella ordini abbandonati (status → `cancelled`, payment_status → `failed`)

#### API di Cleanup Automatico (Cron)
**Endpoint**: `/api/cron/cleanup-abandoned-orders`

- Eseguito automaticamente ogni giorno alle 02:00
- Cancella ordini pending da più di 24 ore
- Cancella anche i Payment Intent su Stripe se possibile

### 3. Interfaccia Admin
**Pagina**: `/admin/cleanup`

- Visualizza ordini abbandonati
- Statistiche (numero ordini, valore totale)
- Pulsanti per refresh, cart recovery manuale e cleanup
- Filtri nella dashboard per ordini "Tutti" vs "Completati"

### 4. Testing e Debug

#### Test Cart Recovery
**Endpoint**: `/api/admin/test-cart-recovery`

- Disponibile solo in development
- Testa email di recupero in tutte le lingue
- Utilizza TEST_USER_EMAIL per i test

## Configurazione

### Variabili d'Ambiente
```env
CRON_SECRET=your-secret-key  # Opzionale, per sicurezza del cron
STRIPE_SECRET_KEY=sk_...     # Per cancellare Payment Intent
```

### Cron Job (Vercel Free Plan)
```json
{
  "crons": [
    {
      "path": "/api/cron/order-management",
      "schedule": "0 2 * * *"
    }
  ]
}
```

**Nota**: Con il piano gratuito di Vercel sono disponibili solo 2 cron jobs che possono essere eseguiti massimo 1 volta al giorno. Per questo motivo, cart recovery e cleanup sono stati unificati in un singolo cron job che viene eseguito quotidianamente alle 2:00.

## Logica di Cleanup

### Criteri per Ordini Abbandonati
- Status: `pending`
- Payment Status: `pending`
- Creato più di 24 ore fa
- **NON hanno ricevuto email di recupero** (`cart_recovery_sent_at` è null)

### Azioni di Cleanup
1. **Database**: Aggiorna ordini a `cancelled` / `failed`
2. **Stripe**: Cancella Payment Intent se in stato cancellabile
3. **Note**: Aggiunge nota "Automatically cancelled - abandoned checkout"

### Protezione Race Condition
Gli ordini che hanno ricevuto email di recupero carrello **NON vengono cancellati** automaticamente, anche se sono più vecchi di 24 ore. Questo evita che un ordine venga cancellato immediatamente dopo l'invio dell'email di recupero.

### Stati Stripe Cancellabili
- `requires_payment_method`
- `requires_confirmation`
- `requires_action`

## Monitoraggio

### Log
- Tutti i cleanup sono loggati con prefisso `🧹`
- Errori Stripe sono loggati ma non bloccano il processo
- Statistiche di cleanup sono incluse nella risposta

### Dashboard Admin
- Filtro "Tutti" vs "Completati" negli ordini recenti
- Pagina dedicata per monitoraggio ordini abbandonati
- Statistiche in tempo reale

## Sicurezza

### Autenticazione Cron
- Header `Authorization: Bearer ${CRON_SECRET}` opzionale
- Verifica admin per endpoint manuali
- Uso di `supabaseAdmin` per bypass RLS

### Gestione Errori
- Rollback automatico in caso di errori
- Continuazione del processo anche se singoli Payment Intent falliscono
- Logging dettagliato per debugging

## Best Practices

### Frequenza Cleanup
- **Automatico**: Una volta al giorno (02:00)
- **Manuale**: Quando necessario tramite admin

### Retention Policy
- Ordini cancellati vengono mantenuti per analytics
- Solo status viene cambiato, dati rimangono intatti

### Monitoraggio
- Controllare regolarmente la pagina admin cleanup
- Verificare log per errori Stripe
- Monitorare metriche di abbandono checkout

## Utilizzo

### Admin Dashboard
1. Vai su `/admin/cleanup`
2. Visualizza ordini abbandonati
3. Clicca "Refresh" per aggiornare
4. Clicca "Cleanup" per pulizia manuale

### Filtri Dashboard
- **Tutti**: Mostra tutti gli ordini recenti
- **Completati**: Mostra solo ordini confirmed/processing/shipped/delivered

### API Diretta
```bash
# Controlla ordini abbandonati
curl GET /api/admin/cleanup/abandoned-orders

# Cleanup manuale
curl POST /api/admin/cleanup/abandoned-orders

# Cron (con secret)
curl GET /api/cron/cleanup-abandoned-orders \
  -H "Authorization: Bearer your-secret"
```

## Troubleshooting

### Ordini non vengono puliti
- Verificare che siano passate più di 24 ore
- Controllare status (deve essere `pending`)
- Verificare log per errori

### Errori Stripe
- Payment Intent già processati non possono essere cancellati
- Errori Stripe non bloccano il cleanup del database
- Controllare log per dettagli specifici

### Performance
- Cleanup è ottimizzato per batch operations
- Limite automatico di 24 ore previene cleanup eccessivo
- Indici database su created_at e status per performance
