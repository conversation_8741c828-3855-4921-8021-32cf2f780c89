import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'
import { sendCartRecoveryEmail } from '@/lib/email'

// Unified cron job for both cart recovery and order cleanup
// Runs once daily at 2:00 AM to work within Vercel free plan limits
export async function GET(request: NextRequest) {
  try {
    // Verify the request is from a trusted source (optional security measure)
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET

    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.log('🔄 Order Management Cron: Unauthorized attempt')
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    console.log('🔄 Order Management Cron: Starting daily order management process...')

    const results = {
      cartRecovery: {
        emailsSent: 0,
        errors: [] as string[]
      },
      cleanup: {
        ordersCleanedUp: 0,
        stripeCancelledCount: 0,
        errors: [] as string[]
      }
    }

    // ===== PART 1: CART RECOVERY (2+ hours old pending orders) =====
    console.log('📧 Cart Recovery: Starting cart recovery process...')

    // Get orders that are pending for 2+ hours and haven't received recovery email yet
    const twoHoursAgo = new Date()
    twoHoursAgo.setHours(twoHoursAgo.getHours() - 2)

    const { data: pendingOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        user_id,
        email,
        status,
        payment_status,
        created_at,
        total_amount,
        order_number,
        cart_recovery_sent_at
      `)
      .eq('status', 'pending')
      .eq('payment_status', 'pending')
      .is('cart_recovery_sent_at', null) // Only orders that haven't received recovery email
      .lt('created_at', twoHoursAgo.toISOString())

    if (fetchError) {
      console.error('📧 Cart Recovery: Error fetching pending orders:', fetchError)
      results.cartRecovery.errors.push(`Failed to fetch pending orders: ${fetchError.message}`)
    } else if (pendingOrders && pendingOrders.length > 0) {
      console.log(`📧 Cart Recovery: Found ${pendingOrders.length} orders for cart recovery`)

      // Process each order for cart recovery
      for (const order of pendingOrders) {
        try {
          console.log(`📧 Cart Recovery: Processing order ${order.id} for ${order.email}`)

          // Get user's preferred language
          let userLocale = 'de' // Default fallback
          if (order.user_id) {
            try {
              const { data: userData } = await supabaseAdmin
                .from('users')
                .select('preferred_language')
                .eq('id', order.user_id)
                .single()

              if (userData?.preferred_language) {
                userLocale = userData.preferred_language
              }
            } catch {
              console.log(`📧 Cart Recovery: Could not fetch user language for ${order.email}, using default:`, userLocale)
            }
          }

          console.log(`📧 Cart Recovery: Sending recovery email to ${order.email} in locale: ${userLocale}`)

          // Send cart recovery email
          await sendCartRecoveryEmail(order, userLocale)

          // Mark order as having received recovery email
          const { error: updateError } = await supabaseAdmin
            .from('orders')
            .update({
              cart_recovery_sent_at: new Date().toISOString()
            })
            .eq('id', order.id)

          if (updateError) {
            console.error(`📧 Cart Recovery: Error updating order ${order.id}:`, updateError)
            results.cartRecovery.errors.push(`Failed to update order ${order.id}: ${updateError.message}`)
          } else {
            results.cartRecovery.emailsSent++
            console.log(`📧 Cart Recovery: Successfully sent recovery email for order ${order.id}`)
          }

        } catch (emailError) {
          const errorMessage = `Failed to send recovery email for order ${order.id} (${order.email}): ${emailError instanceof Error ? emailError.message : 'Unknown error'}`
          console.error(`📧 Cart Recovery: ${errorMessage}`)
          results.cartRecovery.errors.push(errorMessage)
        }
      }
    } else {
      console.log('📧 Cart Recovery: No pending orders found for recovery')
    }

    // ===== PART 2: CLEANUP (24+ hours old pending orders) =====
    console.log('🧹 Cleanup: Starting abandoned orders cleanup...')

    // Get orders that are pending for more than 24 hours
    const twentyFourHoursAgo = new Date()
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24)

    // Find abandoned orders (pending status, older than 24 hours)
    // EXCLUDE orders that have received cart recovery email - give them more time
    const { data: abandonedOrders, error: abandonedFetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        payment_intent_id,
        status,
        payment_status,
        created_at,
        email,
        total_amount,
        cart_recovery_sent_at
      `)
      .eq('status', 'pending')
      .eq('payment_status', 'pending')
      .lt('created_at', twentyFourHoursAgo.toISOString())
      .is('cart_recovery_sent_at', null) // Only cleanup orders that haven't received recovery email

    if (abandonedFetchError) {
      console.error('🧹 Cleanup: Error fetching abandoned orders:', abandonedFetchError)
      results.cleanup.errors.push(`Failed to fetch abandoned orders: ${abandonedFetchError.message}`)
    } else if (abandonedOrders && abandonedOrders.length > 0) {
      console.log(`🧹 Cleanup: Found ${abandonedOrders.length} abandoned orders to clean up`)

      // Cancel abandoned orders
      const { data: cancelledOrders, error: cancelError } = await supabaseAdmin
        .from('orders')
        .update({
          status: 'cancelled',
          payment_status: 'failed',
          notes: 'Automatically cancelled - abandoned checkout after 24 hours'
        })
        .in('id', abandonedOrders.map(order => order.id))
        .select()

      if (cancelError) {
        console.error('🧹 Cleanup: Error cancelling abandoned orders:', cancelError)
        results.cleanup.errors.push(`Failed to cancel abandoned orders: ${cancelError.message}`)
      } else {
        results.cleanup.ordersCleanedUp = cancelledOrders?.length || 0

        // Cancel Stripe Payment Intents for abandoned orders
        const { default: Stripe } = await import('stripe')
        const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!)

        for (const order of abandonedOrders) {
          if (order.payment_intent_id) {
            try {
              const paymentIntent = await stripe.paymentIntents.retrieve(order.payment_intent_id)
              
              // Only cancel if the payment intent is still in a cancellable state
              if (['requires_payment_method', 'requires_confirmation', 'requires_action'].includes(paymentIntent.status)) {
                await stripe.paymentIntents.cancel(order.payment_intent_id)
                results.cleanup.stripeCancelledCount++
                console.log(`🧹 Cleanup: Cancelled Stripe Payment Intent ${order.payment_intent_id}`)
              } else {
                console.log(`🧹 Cleanup: Payment Intent ${order.payment_intent_id} is in status ${paymentIntent.status}, skipping cancellation`)
              }
            } catch (stripeError: unknown) {
              const errorMessage = `Failed to cancel Payment Intent ${order.payment_intent_id}: ${stripeError instanceof Error ? stripeError.message : 'Unknown error'}`
              console.error(`🧹 Cleanup: ${errorMessage}`)
              results.cleanup.errors.push(errorMessage)
            }
          }
        }
      }
    } else {
      console.log('🧹 Cleanup: No abandoned orders found')
    }

    // ===== FINAL RESULTS =====
    const finalResult = {
      success: true,
      message: `Order management completed. Cart recovery: ${results.cartRecovery.emailsSent} emails sent. Cleanup: ${results.cleanup.ordersCleanedUp} orders cancelled.`,
      timestamp: new Date().toISOString(),
      cartRecovery: {
        emailsSent: results.cartRecovery.emailsSent,
        errors: results.cartRecovery.errors.length > 0 ? results.cartRecovery.errors : undefined
      },
      cleanup: {
        ordersCleanedUp: results.cleanup.ordersCleanedUp,
        stripeCancelledCount: results.cleanup.stripeCancelledCount,
        errors: results.cleanup.errors.length > 0 ? results.cleanup.errors : undefined
      }
    }

    console.log(`🔄 Order Management Cron: Process completed successfully. Cart recovery: ${results.cartRecovery.emailsSent} emails, Cleanup: ${results.cleanup.ordersCleanedUp} orders`)

    return NextResponse.json(finalResult)

  } catch (error) {
    console.error('🔄 Order Management Cron: Error in order management process:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    )
  }
}

// POST method for manual triggering (same logic as GET)
export async function POST(request: NextRequest) {
  return GET(request)
}
