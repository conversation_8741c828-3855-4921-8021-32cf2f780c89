'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { useLocale } from 'next-intl'
import { createClient } from '@/lib/supabase/client'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, AlertTriangle, CheckCircle, Clock, Mail } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import { AdminBackButton } from '@/components/admin/admin-back-button'

interface AbandonedOrder {
  id: string
  email: string
  total_amount: number
  created_at: string
  hours_since_creation: number
}

export default function AdminCleanupPage() {
  const locale = useLocale()
  const router = useRouter()
  const supabase = useMemo(() => createClient(), [])

  const [loading, setLoading] = useState(true)
  const [authChecked, setAuthChecked] = useState(false)
  const [abandonedOrders, setAbandonedOrders] = useState<AbandonedOrder[]>([])
  const [refreshLoading, setRefreshLoading] = useState(false)
  const [orderManagementLoading, setOrderManagementLoading] = useState(false)
  const { toast } = useToast()

  const loadAbandonedOrders = useCallback(async () => {
    try {
      setRefreshLoading(true)
      const response = await fetch('/api/admin/cleanup/abandoned-orders')
      const result = await response.json()

      if (result.success) {
        setAbandonedOrders(result.abandonedOrders || [])
      } else {
        toast({
          title: 'Errore',
          description: 'Errore nel caricamento degli ordini abbandonati',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error loading abandoned orders:', error)
      toast({
        title: 'Errore',
        description: 'Errore nel caricamento degli ordini abbandonati',
        variant: 'destructive'
      })
    } finally {
      setRefreshLoading(false)
      setLoading(false)
    }
  }, [toast])

  useEffect(() => {
    async function checkAuthAndLoadData() {
      try {
        if (authChecked) return

        console.log('Checking authentication...')
        const { data: { user }, error: authError } = await supabase.auth.getUser()

        if (authError) {
          console.error('Auth error:', authError)
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        if (!user) {
          console.log('No user found, redirecting to login')
          setAuthChecked(true)
          router.push(`/${locale}/login`)
          return
        }

        // Check if user is admin
        const { data: profile, error: profileError } = await supabase
          .from('users')
          .select('is_admin')
          .eq('id', user.id)
          .single()

        if (profileError || !profile?.is_admin) {
          console.log('User is not admin, redirecting')
          setAuthChecked(true)
          router.push(`/${locale}`)
          return
        }

        setAuthChecked(true)
        await loadAbandonedOrders()
      } catch (error) {
        console.error('Error in auth check:', error)
        setAuthChecked(true)
        router.push(`/${locale}/login`)
      }
    }

    checkAuthAndLoadData()
  }, [supabase, router, locale, authChecked, loadAbandonedOrders])



  const handleOrderManagement = async () => {
    try {
      setOrderManagementLoading(true)
      const response = await fetch('/api/cron/order-management', {
        method: 'POST'
      })
      const result = await response.json()

      if (result.success) {
        const emailsSent = result.cartRecovery?.emailsSent || 0
        const ordersCleanedUp = result.cleanup?.ordersCleanedUp || 0

        toast({
          title: 'Successo',
          description: `Gestione ordini completata: ${emailsSent} email di recupero inviate, ${ordersCleanedUp} ordini puliti`,
          variant: 'default'
        })
        await loadAbandonedOrders() // Refresh the list
      } else {
        toast({
          title: 'Errore',
          description: result.error || 'Errore durante la gestione degli ordini',
          variant: 'destructive'
        })
      }
    } catch (error) {
      console.error('Error during order management:', error)
      toast({
        title: 'Errore',
        description: 'Errore durante la gestione degli ordini',
        variant: 'destructive'
      })
    } finally {
      setOrderManagementLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('it-CH', {
      style: 'currency',
      currency: 'CHF'
    }).format(amount)
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p>Caricamento...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Back to Dashboard Button */}
      <div className="mb-6">
        <AdminBackButton />
      </div>

      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Gestione Ordini Abbandonati</h1>
        <p className="text-muted-foreground">
          Gestisci ordini abbandonati, invia email di recupero carrello e pulisci ordini vecchi
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid md:grid-cols-3 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Ordini Abbandonati</p>
                <p className="text-2xl font-bold">{abandonedOrders.length}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Valore Totale</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(abandonedOrders.reduce((sum, order) => sum + order.total_amount, 0))}
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Azioni</p>
                <div className="flex gap-2 mt-2">
                  <Button
                    onClick={loadAbandonedOrders}
                    disabled={refreshLoading}
                    variant="outline"
                    size="sm"
                    title="Aggiorna lista"
                  >
                    {refreshLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <RefreshCw className="h-4 w-4" />
                    )}
                  </Button>
                  <Button
                    onClick={handleOrderManagement}
                    disabled={orderManagementLoading}
                    variant="outline"
                    size="sm"
                    title="Esegui gestione ordini completa (recovery + cleanup)"
                  >
                    {orderManagementLoading ? (
                      <RefreshCw className="h-4 w-4 animate-spin" />
                    ) : (
                      <Mail className="h-4 w-4" />
                    )}
                  </Button>

                </div>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Abandoned Orders List */}
      <Card>
        <CardHeader>
          <CardTitle>Ordini Abbandonati</CardTitle>
        </CardHeader>
        <CardContent>
          {abandonedOrders.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <CheckCircle className="h-12 w-12 mx-auto mb-4 text-green-500" />
              <p>Nessun ordine abbandonato trovato!</p>
              <p className="text-sm">Tutti gli ordini sono stati processati correttamente.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {abandonedOrders.map((order) => (
                <div
                  key={order.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
                >
                  <div>
                    <div className="font-medium">{order.email}</div>
                    <div className="text-sm text-muted-foreground">
                      Creato: {new Date(order.created_at).toLocaleString('it-CH')}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      ID: {order.id.slice(0, 8)}...
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">{formatCurrency(order.total_amount)}</div>
                    <Badge variant="destructive">
                      {order.hours_since_creation}h fa
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
