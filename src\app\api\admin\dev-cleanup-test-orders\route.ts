import { NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase/admin'

// Development-only endpoint to clean up test orders
export async function POST() {
  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      console.log('🧪 Dev Cleanup: Only available in development')
      return NextResponse.json(
        { error: 'Dev cleanup endpoint only available in development environment' },
        { status: 403 }
      )
    }

    console.log('🧪 Dev Cleanup: Starting test orders cleanup...')

    // Test user emails to clean up
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    // Find test orders
    const { data: testOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        order_number,
        email,
        status,
        payment_status,
        created_at,
        total_amount
      `)
      .in('email', testEmails)
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('🧪 Dev Cleanup: Error fetching test orders:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch test orders' },
        { status: 500 }
      )
    }

    if (!testOrders || testOrders.length === 0) {
      console.log('🧪 Dev Cleanup: No test orders found')
      return NextResponse.json({
        success: true,
        message: 'No test orders found to clean up',
        deletedCount: 0,
        testEmails
      })
    }

    console.log(`🧪 Dev Cleanup: Found ${testOrders.length} test orders to delete`)

    // Delete test orders (this will cascade delete order_items due to foreign key constraints)
    const { error: deleteError } = await supabaseAdmin
      .from('orders')
      .delete()
      .in('email', testEmails)

    if (deleteError) {
      console.error('🧪 Dev Cleanup: Error deleting test orders:', deleteError)
      return NextResponse.json(
        { error: 'Failed to delete test orders' },
        { status: 500 }
      )
    }

    const result = {
      success: true,
      message: `Successfully deleted ${testOrders.length} test orders`,
      deletedCount: testOrders.length,
      testEmails,
      deletedOrders: testOrders.map(order => ({
        id: order.id,
        order_number: order.order_number,
        email: order.email,
        status: order.status,
        payment_status: order.payment_status,
        total_amount: order.total_amount,
        created_at: order.created_at,
        hours_since_creation: Math.floor(
          (new Date().getTime() - new Date(order.created_at).getTime()) / (1000 * 60 * 60)
        )
      }))
    }

    console.log(`🧪 Dev Cleanup: Successfully deleted ${testOrders.length} test orders`)

    return NextResponse.json(result)

  } catch (error) {
    console.error('🧪 Dev Cleanup: Error in dev cleanup process:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}

// GET method to check what test orders would be deleted
export async function GET() {
  try {
    // Check if this is a development environment
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Dev cleanup endpoint only available in development environment' },
        { status: 403 }
      )
    }

    console.log('🧪 Dev Cleanup: Checking test orders...')

    // Test user emails to check
    const testEmails = [
      '<EMAIL>',
      '<EMAIL>'
    ]

    // Find test orders
    const { data: testOrders, error: fetchError } = await supabaseAdmin
      .from('orders')
      .select(`
        id,
        order_number,
        email,
        status,
        payment_status,
        created_at,
        total_amount
      `)
      .in('email', testEmails)
      .order('created_at', { ascending: false })

    if (fetchError) {
      console.error('🧪 Dev Cleanup: Error fetching test orders:', fetchError)
      return NextResponse.json(
        { error: 'Failed to fetch test orders' },
        { status: 500 }
      )
    }

    const result = {
      success: true,
      message: `Found ${testOrders?.length || 0} test orders`,
      testEmails,
      testOrders: testOrders?.map(order => ({
        id: order.id,
        order_number: order.order_number,
        email: order.email,
        status: order.status,
        payment_status: order.payment_status,
        total_amount: order.total_amount,
        created_at: order.created_at,
        hours_since_creation: Math.floor(
          (new Date().getTime() - new Date(order.created_at).getTime()) / (1000 * 60 * 60)
        )
      })) || []
    }

    return NextResponse.json(result)

  } catch (error) {
    console.error('🧪 Dev Cleanup: Error checking test orders:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    )
  }
}
